version: '3.8'

services:
  tpa-api:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: tpa-api
    ports:
      - "9000:9000"
    environment:
      # Application settings
      - APP_NAME=TPA API
      - APP_VERSION=1.0.0
      - DEBUG=true

      # Server settings
      - HOST=0.0.0.0
      - PORT=9000
      - WORKERS=1

      # API settings
      - API_PREFIX=/api
      - CORS_ORIGINS=["*"]

      # Data settings
      - CSV_DATA_PATH=data/csv
      - TIMEZONE_OFFSET=7

      # Logging settings
      - LOG_LEVEL=DEBUG
      - LOG_FORMAT=json
    volumes:
      # Mount source code for development (comment out for production)
      - ../src:/app/src:ro
      - ../config:/app/config:ro
      - ../data:/app/data:ro

      # Mount logs directory for persistence
      - tpa-api-logs:/app/logs

    # Health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

    # Restart policy
    restart: unless-stopped

    # Resource limits
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

volumes:
  tpa-api-logs:
    driver: local

networks:
  default:
    name: tpa-api-network
