# TPA API Comprehensive Parameter Reference

## Overview
This document provides a complete reference for all available parameters in the TPA API endpoints, including their valid values derived from the CSV mock data.

## API Base Information
- **Base URL**: `http://localhost:9000`
- **API Prefix**: `/api`
- **Content-Type**: `application/json`
- **All parameters are passed as query parameters**

## API Endpoints Summary
1. **PolicyListSF** - Search and list insurance policies (10 parameter combinations)
2. **PolicyDetailSF** - Get detailed policy information (1 parameter combination)
3. **ClaimListSF** - List claims history (2 parameter combinations)

---

## 1. PolicyListSF Endpoint

**URL**: `GET /api/PolicyListSF`

**Description**: Search and list insurance policies using 10 different parameter combinations.

### Parameter Combinations (Must use exactly one):

#### Combination 1: INSURER_CODE + CITIZEN_ID
- `INSURER_CODE` (required): Insurance company code
- `CITIZEN_ID` (required): Citizen identification number

#### Combination 2: INSURER_CODE + POLICY_NO + NAME_TH
- `INSURER_CODE` (required): Insurance company code  
- `POLICY_NO` (required): Policy number
- `NAME_TH` (required): Thai name (exact match, case-sensitive)

#### Combination 3: INSURER_CODE + POLICY_NO + NAME_EN
- `INSURER_CODE` (required): Insurance company code
- `POLICY_NO` (required): Policy number
- `NAME_EN` (required): English name (exact match, case-sensitive)

#### Combination 4: INSURER_CODE + CERTIFICATE_NO + NAME_TH
- `INSURER_CODE` (required): Insurance company code
- `CERTIFICATE_NO` (required): Certificate number
- `NAME_TH` (required): Thai name (exact match, case-sensitive)

#### Combination 5: INSURER_CODE + CERTIFICATE_NO + NAME_EN
- `INSURER_CODE` (required): Insurance company code
- `CERTIFICATE_NO` (required): Certificate number
- `NAME_EN` (required): English name (exact match, case-sensitive)

#### Combination 6: INSURER_CODE + STAFF_NO + NAME_TH
- `INSURER_CODE` (required): Insurance company code
- `STAFF_NO` (required): Staff number
- `NAME_TH` (required): Thai name (exact match, case-sensitive)

#### Combination 7: INSURER_CODE + STAFF_NO + NAME_EN
- `INSURER_CODE` (required): Insurance company code
- `STAFF_NO` (required): Staff number
- `NAME_EN` (required): English name (exact match, case-sensitive)

#### Combination 8: INSURER_CODE + OTHER_ID
- `INSURER_CODE` (required): Insurance company code
- `OTHER_ID` (required): Other identification

#### Combination 9: INSURER_CODE + NAME_TH
- `INSURER_CODE` (required): Insurance company code
- `NAME_TH` (required): Thai name (exact match, case-sensitive)

#### Combination 10: INSURER_CODE + NAME_EN
- `INSURER_CODE` (required): Insurance company code
- `NAME_EN` (required): English name (exact match, case-sensitive)

### Available Parameter Values:

**INSURER_CODE** (Insurance Company Codes):
- `INS001` - บริษัท ประกันภัย เอ จำกัด (Insurance Company A Ltd.)
- `INS002` - Health Insurance Co. Ltd.
- `INS003` - บริษัท ประกันชีวิต ซี จำกัด (Life Insurance C Ltd.)

**CITIZEN_ID** (Citizen ID Numbers):
- `1234567890123` - สมชาย ใจดี (Somchai Jaidee)
- `1234567890124` - สุดา รักดี (Suda Rakdee)
- `1234567890125` - มาลี สวยงาม (Malee Suayngam)
- `1234567890126` - วิชัย เก่งมาก (Wichai Kengmak)
- `1234567890127` - นิดา ขยันดี (Nida Khayanee)
- `1234567890128` - ประยุทธ มั่นคง (Prayuth Mankong)
- `1234567890129` - สมหญิง ดีใจ (Somying Deejai)
- `1234567890130` - อนุชา รวยมาก (Anucha Ruaimak)
- `1234567890131` - ปิยะดา สุขใส (Piyada Suksai)
- `1234567890132` - ธนากร เจริญรุ่ง (Thanakorn Charoenrung)
- `1234567890133` - สมศักดิ์ หมดอายุ (Somsak Modayu)
- `1234567890134` - วรรณา ใหม่สด (Wanna Maisod)

**POLICY_NO** (Policy Numbers):
- `POL001`, `POL002`, `POL003`, `POL004`, `POL005`, `POL006`, `POL007`, `POL008`, `POL009`, `POL010`

**CERTIFICATE_NO** (Certificate Numbers):
- `CERT001`, `CERT002`, `CERT003`, `CERT004`, `CERT005`, `CERT006`, `CERT007`, `CERT008`, `CERT009`, `CERT010`, `CERT011`, `CERT012`

**STAFF_NO** (Staff Numbers):
- `ST001`, `ST002`, `ST003`, `ST004`, `ST005`, `ST006`, `ST007`, `ST008`, `ST009`, `ST010`, `ST011`, `ST012`

**OTHER_ID** (Other ID Numbers):
- `EMP001`, `EMP002`, `EMP003`, `EMP004`, `EMP005`, `EMP006`, `EMP007`, `EMP008`, `EMP009`, `EMP010`, `EMP011`, `EMP012`

**NAME_TH** (Thai Names - exact match, case-sensitive):
- `สมชาย ใจดี`, `สุดา รักดี`, `มาลี สวยงาม`, `วิชัย เก่งมาก`, `นิดา ขยันดี`, `ประยุทธ มั่นคง`, `สมหญิง ดีใจ`, `อนุชา รวยมาก`, `ปิยะดา สุขใส`, `ธนากร เจริญรุ่ง`, `สมศักดิ์ หมดอายุ`, `วรรณา ใหม่สด`

**NAME_EN** (English Names - exact match, case-sensitive):
- `Somchai Jaidee`, `Suda Rakdee`, `Malee Suayngam`, `Wichai Kengmak`, `Nida Khayanee`, `Prayuth Mankong`, `Somying Deejai`, `Anucha Ruaimak`, `Piyada Suksai`, `Thanakorn Charoenrung`, `Somsak Modayu`, `Wanna Maisod`

### Sample Usage Examples:

```bash
# Example 1: Search by INSURER_CODE + CITIZEN_ID
GET /api/PolicyListSF?INSURER_CODE=INS001&CITIZEN_ID=1234567890123

# Example 2: Search by INSURER_CODE + POLICY_NO + NAME_TH
GET /api/PolicyListSF?INSURER_CODE=INS001&POLICY_NO=POL001&NAME_TH=สมชาย ใจดี

# Example 3: Search by INSURER_CODE + NAME_EN
GET /api/PolicyListSF?INSURER_CODE=INS002&NAME_EN=Wichai Kengmak

# Example 4: Search by INSURER_CODE + CERTIFICATE_NO + NAME_EN
GET /api/PolicyListSF?INSURER_CODE=INS001&CERTIFICATE_NO=CERT003&NAME_EN=Malee Suayngam

# Example 5: Search by INSURER_CODE + STAFF_NO + NAME_TH
GET /api/PolicyListSF?INSURER_CODE=INS003&STAFF_NO=ST006&NAME_TH=ประยุทธ มั่นคง

# Example 6: Search by INSURER_CODE + OTHER_ID
GET /api/PolicyListSF?INSURER_CODE=INS002&OTHER_ID=EMP008
```

---

## 2. PolicyDetailSF Endpoint

**URL**: `GET /api/PolicyDetailSF`

**Description**: Get comprehensive policy information for a specific member including policy details, benefits, contract conditions, member conditions, and claim history.

### Parameters:

**MEMBER_CODE** (required): Member code to retrieve policy details for

### Available Parameter Values:

**MEMBER_CODE** (Member Codes):
- `MEM001` - สมชาย ใจดี (Somchai Jaidee) - Active
- `MEM002` - สุดา รักดี (Suda Rakdee) - Active
- `MEM003` - มาลี สวยงาม (Malee Suayngam) - Active, VIP
- `MEM004` - วิชัย เก่งมาก (Wichai Kengmak) - Active
- `MEM005` - นิดา ขยันดี (Nida Khayanee) - Active
- `MEM006` - ประยุทธ มั่นคง (Prayuth Mankong) - Active, VIP
- `MEM007` - สมหญิง ดีใจ (Somying Deejai) - Active
- `MEM008` - อนุชา รวยมาก (Anucha Ruaimak) - Active, VIP
- `MEM009` - ปิยะดา สุขใส (Piyada Suksai) - Active
- `MEM010` - ธนากร เจริญรุ่ง (Thanakorn Charoenrung) - Active
- `MEM011` - สมศักดิ์ หมดอายุ (Somsak Modayu) - Inactive
- `MEM012` - วรรณา ใหม่สด (Wanna Maisod) - Active

### Sample Usage Examples:

```bash
# Example 1: Get detailed policy information for active member
GET /api/PolicyDetailSF?MEMBER_CODE=MEM001

# Example 2: Get detailed policy information for VIP member
GET /api/PolicyDetailSF?MEMBER_CODE=MEM003

# Example 3: Get detailed policy information for inactive member
GET /api/PolicyDetailSF?MEMBER_CODE=MEM011
```

---

## 3. ClaimListSF Endpoint

**URL**: `GET /api/ClaimListSF`

**Description**: List claims history for members using 2 different parameter combinations.

### Parameter Combinations (Must use exactly one):

#### Combination 1: MEMBER_CODE
- `MEMBER_CODE` (required): Member code

#### Combination 2: INSURER_CODE + CITIZEN_ID
- `INSURER_CODE` (required): Insurance company code
- `CITIZEN_ID` (required): Citizen identification number

### Available Parameter Values:

**MEMBER_CODE**: Same as PolicyDetailSF endpoint (MEM001-MEM012)

**INSURER_CODE**: Same as PolicyListSF endpoint (INS001, INS002, INS003)

**CITIZEN_ID**: Same as PolicyListSF endpoint (1234567890123-1234567890134)

### Sample Usage Examples:

```bash
# Example 1: Get claims by MEMBER_CODE
GET /api/ClaimListSF?MEMBER_CODE=MEM001

# Example 2: Get claims by INSURER_CODE + CITIZEN_ID
GET /api/ClaimListSF?INSURER_CODE=INS001&CITIZEN_ID=1234567890123

# Example 3: Get claims for VIP member
GET /api/ClaimListSF?MEMBER_CODE=MEM006
```

---

## Additional Information

### Claim Statuses (returned in ClaimListSF):
- `Approved` - Claim approved
- `Authorized` - Claim authorized  
- `Open` - Claim open
- `Paid` - Claim paid
- `Pending` - Claim pending
- `Pending For Approval` - Claim pending for approval
- `Rejected` - Claim rejected

### Member Statuses:
- `Active` - Active member (can access all services)
- `Inactive` - Inactive member (limited access)

### Card Types:
- `Standard` - Standard card
- `Gold` - Gold card (premium benefits)
- `Platinum` - Platinum card (executive benefits)
- `Diamond` - Diamond card (highest tier benefits)

### VIP Status:
- `Y` - VIP member (special privileges and services)
- `N` - Non-VIP member (standard services)

### Languages:
- `TH` - Thai language preference
- `EN` - English language preference

### Member Types:
- `Principal` - Primary policy holder
- `Dependent` - Dependent member under principal

---

## Error Handling

All endpoints return structured error responses for:
- **400 Bad Request**: Invalid parameter combinations or missing required parameters
- **404 Not Found**: Member not found or no data matching criteria
- **422 Unprocessable Entity**: Validation errors
- **500 Internal Server Error**: Server errors

### Example Error Response:
```json
{
  "error": "Validation Error",
  "message": "Invalid parameter combination. Must provide INSURER_CODE with one of: CITIZEN_ID, (POLICY_NO + NAME_TH), (POLICY_NO + NAME_EN), (CERTIFICATE_NO + NAME_TH), (CERTIFICATE_NO + NAME_EN), (STAFF_NO + NAME_TH), (STAFF_NO + NAME_EN), OTHER_ID, NAME_TH, NAME_EN",
  "details": {}
}
```

---

## Data Relationships and Cross-References

### Member-Policy Relationships:
- Each member has a unique `MemberCode` (MEM001-MEM012)
- Members are linked to policies via `PolicyNo` and `InsurerCode`
- Some members are dependents of principal members
- Claims are linked to members via `MemberCode` and cross-referenced via `CitizenID`

### Insurance Company Distribution:
- **INS001**: 6 members (MEM001, MEM002, MEM003, MEM007, MEM010, MEM011)
- **INS002**: 4 members (MEM004, MEM005, MEM008, MEM012)
- **INS003**: 2 members (MEM006, MEM009)

### VIP Members and Special Benefits:
- **MEM003** (มาลี สวยงาม): VIP Customer, Gold Card
- **MEM006** (ประยุทธ มั่นคง): Executive VIP, Platinum Card
- **MEM008** (อนุชา รวยมาก): High Net Worth, Diamond Card

### Principal-Dependent Relationships:
- **MEM001** (Principal) ← **MEM002** (Dependent)
- **MEM003** (Principal)
- **MEM004** (Principal) ← **MEM005** (Dependent)
- **MEM006** (Principal)
- **MEM007** (Principal)
- **MEM008** (Principal)
- **MEM009** (Principal)
- **MEM010** (Principal)
- **MEM011** (Principal, Inactive)
- **MEM012** (Principal)

---

## Advanced Usage Patterns

### Testing Different Scenarios:

#### 1. Active vs Inactive Members:
```bash
# Active member with claims
GET /api/PolicyDetailSF?MEMBER_CODE=MEM001

# Inactive member (expired policy)
GET /api/PolicyDetailSF?MEMBER_CODE=MEM011
```

#### 2. VIP vs Standard Members:
```bash
# VIP member with premium benefits
GET /api/PolicyDetailSF?MEMBER_CODE=MEM003

# Standard member
GET /api/PolicyDetailSF?MEMBER_CODE=MEM002
```

#### 3. Different Insurance Companies:
```bash
# Insurance Company A (INS001)
GET /api/PolicyListSF?INSURER_CODE=INS001&CITIZEN_ID=1234567890123

# Health Insurance Co. (INS002)
GET /api/PolicyListSF?INSURER_CODE=INS002&CITIZEN_ID=1234567890126

# Life Insurance C (INS003)
GET /api/PolicyListSF?INSURER_CODE=INS003&CITIZEN_ID=1234567890128
```

#### 4. Claims with Different Statuses:
```bash
# Member with paid claims
GET /api/ClaimListSF?MEMBER_CODE=MEM001

# Member with pending claims
GET /api/ClaimListSF?MEMBER_CODE=MEM004

# Member with rejected claims
GET /api/ClaimListSF?MEMBER_CODE=MEM010
```

#### 5. Language Preferences:
```bash
# Thai language preference members
GET /api/PolicyListSF?INSURER_CODE=INS001&NAME_TH=สมชาย ใจดี

# English language preference members
GET /api/PolicyListSF?INSURER_CODE=INS002&NAME_EN=Wichai Kengmak
```

---

## Data Validation Rules

### Parameter Validation:
1. **Exact Match Required**: All name searches (NAME_TH, NAME_EN) require exact case-sensitive matches
2. **Required Combinations**: Cannot mix parameters from different combinations
3. **Non-Empty Values**: Empty strings are converted to null and treated as missing parameters
4. **Single Combination**: Must use exactly one valid parameter combination per request

### Business Logic Validation:
1. **Active Status**: Only active members return full policy details
2. **Valid Claims**: Only claims with valid statuses are returned
3. **Date Ranges**: All dates use Thailand timezone (UTC+7)
4. **Cross-Reference**: INSURER_CODE + CITIZEN_ID combinations must exist in policy data

---

## Performance Considerations

### Optimal Query Patterns:
1. **Direct Member Lookup**: Use `MEMBER_CODE` when available (fastest)
2. **Citizen ID Lookup**: Use `INSURER_CODE + CITIZEN_ID` for cross-reference
3. **Name Searches**: Require exact matches, no partial matching supported

### Response Size Expectations:
- **PolicyListSF**: Returns 1-2 records typically
- **PolicyDetailSF**: Returns comprehensive data (5+ sections)
- **ClaimListSF**: Returns 0-3 claims per member typically

---

## Health Check and Root Endpoints

### Health Check
**URL**: `GET /health`
**Description**: Check API health status
**Response**: Returns server status and timestamp

### Root Endpoint
**URL**: `GET /`
**Description**: API information and available endpoints
**Response**: Returns welcome message and endpoint URLs

---

## Complete Testing Dataset Summary

### Total Records Available:
- **Members**: 12 (11 active, 1 inactive)
- **Policies**: 10 unique policy numbers
- **Claims**: 13 claims across various statuses
- **Benefits**: 13 benefit records
- **Payment Methods**: 17 payment method records
- **Contract Conditions**: 12 condition records
- **Member Conditions**: 11 member-specific conditions

### Coverage by Insurance Company:
- **INS001**: 6 members, 6 claims
- **INS002**: 4 members, 4 claims
- **INS003**: 2 members, 3 claims

This comprehensive dataset supports thorough testing of all API endpoints and parameter combinations.
