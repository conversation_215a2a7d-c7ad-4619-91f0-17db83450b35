[tool.poetry]
name = "tpa-api"
version = "0.1.0"
description = "TPA API for insurance claims and policy management"
authors = ["Developer"]
readme = "README.md"
packages = [{include = "src"}]

[tool.poetry.dependencies]
python = "^3.13"
fastapi = "^0.115.0"
uvicorn = {extras = ["standard"], version = "^0.32.0"}
pydantic = "^2.10.0"
pydantic-settings = "^2.6.0"
python-multipart = "^0.0.12"
python-dateutil = "^2.9.0"

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.0"
requests = "^2.32.3"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"